import React from 'react';

import { Lock } from 'lucide-react';

import FeatureTooltip from '@/core/components/feature-tooltip';

interface AccessDeniedCardProps {
  text: string;
  iconClassName?: string;
  textClassName?: string;
  containerClassName?: string;
  fullHeight?: boolean;
  horizontal?: boolean;
  tooltipTitle?: string;
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right' | 'auto';
}

const AccessDeniedCard: React.FC<AccessDeniedCardProps> = ({
  text,
  iconClassName = 'w-5 h-5 mb-1 text-red-600',
  textClassName = 'text-xs text-center p-2',
  containerClassName = '',
  fullHeight = true,
  horizontal = false,
  tooltipTitle = 'Access Denied',
  tooltipPlacement = 'auto',
}) => {
  return (
    <div
      className={`flex ${horizontal ? 'flex-row items-center' : 'flex-col items-center justify-center'} ${fullHeight ? 'h-full' : ''} bg-green-200 rounded-base cursor-not-allowed ${horizontal ? 'gap-2' : 'gap-1'} ${containerClassName}`}
      style={{
        background:
          'linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(194, 233, 254, 0.8) 100%)',
      }}
    >
      <FeatureTooltip title={tooltipTitle} placement={tooltipPlacement}>
        <Lock className={`${iconClassName} text-red-600`} />
      </FeatureTooltip>
      <FeatureTooltip title={tooltipTitle} placement={tooltipPlacement}>
        <span className={textClassName}>{text}</span>
      </FeatureTooltip>
    </div>
  );
};

export default AccessDeniedCard;
