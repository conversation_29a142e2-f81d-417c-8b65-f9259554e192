'use client';

import { useState, useEffect } from 'react';

import CarouselNavigation from '@/views/subscription/components/CarouselNavigation';
import PlanCard, { Plan } from '@/views/subscription/PlanCard';

interface SmallPlanGridProps {
  plans: Plan[];
  billingCycle: 'monthly' | 'yearly';
  selectedPlan: string | null;
  onPlanSelect: (planId: string) => void;
  onGetQuote?: () => void;
  onSubscribe?: (planId: string) => void;
  isLoading?: boolean;
  plansPerPage?: number;
  isCompact?: boolean;
  isFromProfile?: boolean;
  currentPlanId?: string;
  onBillingCycleChange?: (cycle: 'monthly' | 'yearly') => void;
}

const SmallPlanGrid = ({
  plans,
  billingCycle,
  selectedPlan,
  onPlanSelect,
  onGetQuote,
  onSubscribe,
  isLoading = false,
  plansPerPage = 3,
  isCompact = false,
  isFromProfile = false,
  currentPlanId,
  onBillingCycleChange,
}: SmallPlanGridProps) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const totalSlides = Math.ceil(plans.length / plansPerPage);
  const showNavigation = plans.length > plansPerPage;

  // Reset carousel to first slide when billing cycle changes
  useEffect(() => {
    setCurrentSlide(0);
  }, [billingCycle]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
  };

  const goToSlide = (slideIndex: number) => {
    setCurrentSlide(slideIndex);
  };

  const getCurrentPlans = () => {
    const startIndex = currentSlide * plansPerPage;
    const endIndex = startIndex + plansPerPage;
    return plans.slice(startIndex, endIndex);
  };

  return (
    <div className="relative flex items-center">
      {/* Carousel Navigation Component */}
      <CarouselNavigation
        showNavigation={showNavigation}
        totalSlides={totalSlides}
        currentSlide={currentSlide}
        onPrevSlide={prevSlide}
        onNextSlide={nextSlide}
        onGoToSlide={goToSlide}
        // arrowSize="sm"
        arrowPosition="inside"
        showDisabledArrows={false}
      />

      {/* Plan Cards Grid Container - Centered with reduced width */}
      <div className="w-full max-w-4xl mx-auto px-8">
        {/* Plan Cards Grid - 3 cards in a row with reduced gap */}
        <div className="grid md:grid-cols-3 gap-3 mb-4">
          {getCurrentPlans().map((plan) => (
            <div
              key={plan.id}
              className={isCompact ? 'scale-90 transform origin-top-left' : ''}
            >
              <PlanCard
                plan={plan}
                billingCycle={billingCycle}
                selectedPlan={selectedPlan}
                onPlanSelect={onPlanSelect}
                onGetQuote={onGetQuote}
                onSubscribe={onSubscribe}
                isLoading={isLoading}
                allPlans={plans}
                isFromProfile={isFromProfile}
                currentPlanId={currentPlanId}
                onBillingCycleChange={onBillingCycleChange}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SmallPlanGrid;
