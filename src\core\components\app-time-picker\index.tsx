import React, { useMemo } from 'react';

import Select from 'react-select';

import InputLabel from '../input-label';

import { getCustomStyles } from './theme';
import { AppTimePickerProps, TimeOption } from './types';
import {
  convertToTime,
  convertToTimeObject,
  getCurrentTime,
  hourOptions,
  minuteOptions,
  periodOptions,
} from './utils';

const AppTimePicker: React.FC<AppTimePickerProps> = ({
  value,
  onChange,
  disabled = false,
  className = '',
  label,
  required = false,
  helperText,
  error,
  slotProps = {
    hourSelect: {},
    minuteSelect: {},
    periodSelect: {},
    select: {},
  },
}) => {
  const defaultTime = useMemo(getCurrentTime, []);

  const isEmpty = value === '' || value === null || value === undefined;
  const selected = isEmpty ? null : convertToTimeObject(value) || defaultTime;

  const handleSelect = (
    type: 'hour' | 'minute' | 'period',
    option: TimeOption
  ) => {
    if (onChange) {
      const baseTime = selected || defaultTime;
      onChange(convertToTime({ ...baseTime, [type]: option.value }));
    }
  };

  return (
    <div className={className}>
      {label && (
        <InputLabel
          label={label}
          required={required}
          className="mb-0 md:mb-0 text-sm md:text-sm"
        />
      )}
      <div className="flex w-full" style={{ gap: 0 }}>
        <Select
          value={
            selected ? hourOptions.find((o) => o.value === selected.hour) : null
          }
          options={hourOptions}
          onChange={(opt) => handleSelect('hour', opt as TimeOption)}
          isDisabled={disabled}
          placeholder="--"
          styles={getCustomStyles('first')}
          classNamePrefix="app-time-picker-hour"
          menuPlacement="auto"
          components={{ IndicatorSeparator: null }}
          {...slotProps.hourSelect}
          {...slotProps.select}
        />
        <Select
          value={
            selected
              ? minuteOptions.find((o) => o.value === selected.minute)
              : null
          }
          options={minuteOptions}
          onChange={(opt) => handleSelect('minute', opt as TimeOption)}
          isDisabled={disabled}
          placeholder="--"
          styles={getCustomStyles('middle')}
          classNamePrefix="app-time-picker-minute"
          menuPlacement="auto"
          components={{ IndicatorSeparator: null }}
          {...slotProps.minuteSelect}
          {...slotProps.select}
        />
        <Select
          value={
            selected
              ? periodOptions.find((o) => o.value === selected.period)
              : null
          }
          options={periodOptions}
          onChange={(opt) => handleSelect('period', opt as TimeOption)}
          isDisabled={disabled}
          placeholder="--"
          styles={getCustomStyles('last')}
          classNamePrefix="app-time-picker-period"
          menuPlacement="auto"
          components={{ IndicatorSeparator: null }}
          {...slotProps.periodSelect}
          {...slotProps.select}
        />
      </div>
      {error && <div className="mt-1 text-xs text-red-600">{helperText}</div>}
    </div>
  );
};

export default AppTimePicker;
