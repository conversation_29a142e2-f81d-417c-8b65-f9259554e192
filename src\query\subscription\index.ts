import {
  OrganizationPlan,
  SubscriptionPlansResponse,
  QuoteRequest,
} from '@/utils/subscription';

import { publicApi, api } from '@/core/lib/interceptor';

// Cache for subscription organization ID to avoid repeated API calls
let cachedSubscriptionOrgId: string | null = null;

/**
 * Organization interface from list-organizations API
 */
interface Organization {
  id: string;
  name: string;
  contactEmail: string;
  contactPersonName: string;
  contactPhone: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  pan: string;
  gstin: string;
  description: string;
  isActive: boolean;
  registrationFee: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Response from list-organizations API
 */
interface ListOrganizationsResponse {
  records: Organization[];
  totalRecords: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Get list of organizations with optional filters
 */
export const listOrganizations = async (
  name?: string,
  status: string = 'all'
): Promise<ListOrganizationsResponse> => {
  const params = new URLSearchParams({
    status,
  });

  if (name) {
    params.append('name', name);
  }

  const response = await publicApi.get<ListOrganizationsResponse>(
    `/organization/v0.1/list-organizations?${params.toString()}`
  );
  return response.data;
};

/**
 * Get the subscription organization ID by finding "Subscription - Organization"
 * Results are cached to avoid repeated API calls
 */
export const getSubscriptionOrganizationId = async (): Promise<string> => {
  // Return cached value if available
  if (cachedSubscriptionOrgId) {
    return cachedSubscriptionOrgId;
  }

  try {
    // Fetch organizations with the exact name filter
    const response = await listOrganizations(
      'Subscription - Organization',
      'all'
    );

    // Find the organization with exact name match
    const subscriptionOrg = response.records.find(
      (org) => org.name === 'Subscription - Organization'
    );

    if (!subscriptionOrg) {
      throw new Error('Subscription organization not found');
    }

    // Cache the ID for future use
    cachedSubscriptionOrgId = subscriptionOrg.id;
    return subscriptionOrg.id;
  } catch (error) {
    console.error('Error fetching subscription organization:', error);
    throw error;
  }
};

/**
 * Get organization custom plan details
 */
export const getOrganizationPlan = async (): Promise<OrganizationPlan> => {
  const response = await publicApi.get<OrganizationPlan>(
    '/subscription/v0.1/organization-plan'
  );
  return response.data;
};

/**
 * Get all subscription plans
 */
export const getSubscriptionPlans = async (
  includeInactive: boolean = true
): Promise<SubscriptionPlansResponse> => {
  const response = await publicApi.get<SubscriptionPlansResponse>(
    `/subscription/v0.1/subscription-plan?includeInactive=${includeInactive}`
  );
  return response.data;
};

/**
 * Submit quote request
 */
export const submitQuoteRequest = async (
  data: QuoteRequest
): Promise<{ success: boolean; message: string }> => {
  const response = await publicApi.post<{ success: boolean; message: string }>(
    '/subscription/v0.1/subscription/quote',
    data
  );
  return response.data;
};

/**
 * Calculate prorated amount for plan upgrade
 */
export const calculateProratedAmount = async (data: {
  currentPlanId: string;
  newPlanId: string;
  billingPeriod: 'monthly' | 'yearly';
  currentPlanStartDate: string;
}): Promise<{
  proratedAmount: number;
  currentPlanRefund: number;
  newPlanAmount: number;
  finalAmount: number;
}> => {
  const response = await publicApi.post<{
    proratedAmount: number;
    currentPlanRefund: number;
    newPlanAmount: number;
    finalAmount: number;
  }>('/subscription/v0.1/calculate-prorated', data);
  return response.data;
};

/**
 * Start trial for free plan
 */
export const startTrial = async (data: {
  email: string;
  name: string;
  phoneNumber?: string | null;
  planId: string;
  billingType: 'monthly' | 'yearly';
  roleId: string;
  userType: string;
  userRole: string;
  Billing: Array<{
    name: string;
    email: string;
    pincode: string;
  }>;
}): Promise<{ success: boolean; message: string; subscription?: any }> => {
  const response = await publicApi.post<any>(
    '/subscription/v0.1/clinic/subscription?action=start-trial',
    data
  );

  // Check if response contains subscription data (indicates success)
  if (response.data?.subscription?.id) {
    return {
      success: true,
      message: 'Trial started successfully',
      subscription: response.data.subscription,
    };
  }

  // Check for success field in response
  if (response.data?.success !== false) {
    return {
      success: true,
      message: 'Trial started successfully',
    };
  }

  // If we get here, something went wrong
  return {
    success: false,
    message: response.data?.message || 'Failed to start trial',
  };
};

/**
 * Subscribe to plan after successful payment
 */
export const subscribeAfterPayment = async (data: {
  email?: string;
  name?: string;
  phoneNumber?: string;
  planId?: string;
  billingType: 'monthly' | 'yearly';
  roleId?: string;
  userType?: string;
  userRole?: string;
  selectedAddOnFeatures: {
    MRD: Array<{
      featureId: string;
      monthlyAmount: number;
      yearlyAmount: number;
    }>;
    EMR: Array<{
      featureId: string;
      monthlyAmount: number;
      yearlyAmount: number;
    }>;
    Billing: Array<{
      featureId: string;
      monthlyAmount: number;
      yearlyAmount: number;
    }>;
  };
  paymentId: string;
  Billing: Array<{
    name: string;
    email: string;
    pincode: string;
  }>;
}): Promise<{ success: boolean; message: string; subscription?: any }> => {
  const response = await publicApi.post<any>(
    '/subscription/v0.1/clinic/subscription?action=subscribe',
    data
  );

  // Check if response contains subscription data (indicates success)
  if (response.data?.subscription?.id) {
    return {
      success: true,
      message: 'Subscription created successfully',
      subscription: response.data.subscription,
    };
  }

  // Check for success field in response
  if (response.data?.success !== false) {
    return {
      success: true,
      message: 'Subscription created successfully',
    };
  }

  // If we get here, something went wrong
  return {
    success: false,
    message: response.data?.message || 'Failed to create subscription',
  };
};

/**
 * Change plan subscription (for profile flows)
 */
export const changePlanSubscription = async (data: {
  email: string;
  newPlanId: string;
  billingType: 'monthly' | 'yearly';
  selectedAddOnFeatures: {
    MRD: Array<{
      featureId: string;
      monthlyAmount: number;
      yearlyAmount: number;
    }>;
    EMR: Array<{
      featureId: string;
      monthlyAmount: number;
      yearlyAmount: number;
    }>;
    Billing: Array<{
      featureId: string;
      monthlyAmount: number;
      yearlyAmount: number;
    }>;
  };
  paymentId: string;
}): Promise<{ success: boolean; message: string; subscription?: any }> => {
  const response = await publicApi.post<any>(
    '/subscription/v0.1/clinic/subscription?action=change-plan',
    data
  );

  // Check if response contains subscription data (indicates success)
  if (response.data?.subscription?.id) {
    return {
      success: true,
      message: 'Plan changed successfully',
      subscription: response.data.subscription,
    };
  }

  // Check for success field in response
  if (response.data?.success !== false) {
    return {
      success: true,
      message: 'Plan changed successfully',
    };
  }

  // If we get here, something went wrong
  return {
    success: false,
    message: response.data?.message || 'Failed to change plan',
  };
};

/**
 * Validate email for existing subscription
 */
export const validateEmail = async (
  email: string
): Promise<{
  isValid: boolean;
  canProceed: boolean;
  message: string;
  existingSubscription?: {
    id: string;
    status: string;
    planName: string;
  };
  existingUser?: any;
}> => {
  const response = await publicApi.post<{
    isValid: boolean;
    canProceed: boolean;
    message: string;
    existingSubscription?: {
      id: string;
      status: string;
      planName: string;
    };
    existingUser?: any;
  }>('/subscription/v0.1/clinic/subscription?action=validate-email', {
    email,
  });
  return response.data;
};

/**
 * Get roles for organization
 * If no organizationId is provided, it will fetch the subscription organization ID
 */
export const getOrganizationRoles = async (
  organizationId?: string,
  page: number = 1,
  pageSize: number = 10
): Promise<{
  currentPage: number;
  items: {
    id: string;
    name: string;
    description: string;
    isDefault: boolean;
    organizationId: string;
  }[];
  totalItemCount: number;
  totalPages: number;
}> => {
  // If no organizationId provided, fetch the subscription organization ID
  const orgId = organizationId || (await getSubscriptionOrganizationId());

  const response = await publicApi.get<{
    currentPage: number;
    items: {
      id: string;
      name: string;
      description: string;
      isDefault: boolean;
      organizationId: string;
    }[];
    totalItemCount: number;
    totalPages: number;
  }>(
    `/role/v0.1/list-roles?page=${page}&pageSize=${pageSize}&organizationId=${orgId}`
  );
  return response.data;
};

/**
 * Get subscription features for a subscriber
 */
export const getSubscriptionFeatures = async (
  subscriberId: string
): Promise<{
  subscriptionId: string;
  subscriberId: string;
  planId: string;
  planName: string;
  status: string;
  features: Array<{
    id: string;
    featureName: string;
    description: string;
    type: string;
    permissionKeys: string[];
    isBaseFeature: boolean;
    moduleType: string;
    subType?: string;
  }>;
  permissionKeys: string[];
  featuresByModule: {
    MRD: Array<{
      id: string;
      featureName: string;
      description: string;
      type: string;
      permissionKeys: string[];
      isBaseFeature: boolean;
      moduleType: string;
      subType?: string;
    }>;
    EMR: Array<{
      id: string;
      featureName: string;
      description: string;
      type: string;
      permissionKeys: string[];
      isBaseFeature: boolean;
      moduleType: string;
      subType?: string;
    }>;
    Billing: Array<{
      id: string;
      featureName: string;
      description: string;
      type: string;
      permissionKeys: string[];
      isBaseFeature: boolean;
      moduleType: string;
      subType?: string;
    }>;
  };
}> => {
  const response = await api.get<{
    subscriptionId: string;
    subscriberId: string;
    planId: string;
    planName: string;
    status: string;
    features: Array<{
      id: string;
      featureName: string;
      description: string;
      type: string;
      permissionKeys: string[];
      isBaseFeature: boolean;
      moduleType: string;
      subType?: string;
    }>;
    permissionKeys: string[];
    featuresByModule: {
      MRD: Array<{
        id: string;
        featureName: string;
        description: string;
        type: string;
        permissionKeys: string[];
        isBaseFeature: boolean;
        moduleType: string;
        subType?: string;
      }>;
      EMR: Array<{
        id: string;
        featureName: string;
        description: string;
        type: string;
        permissionKeys: string[];
        isBaseFeature: boolean;
        moduleType: string;
        subType?: string;
      }>;
      Billing: Array<{
        id: string;
        featureName: string;
        description: string;
        type: string;
        permissionKeys: string[];
        isBaseFeature: boolean;
        moduleType: string;
        subType?: string;
      }>;
    };
  }>(`/subscription/v0.1/subscription/features?subscriberId=${subscriberId}`);
  return response.data;
};

/**
 * Get subscriber data with subscription details
 */
export const getSubscriber = async (
  subscriberId: string
): Promise<{
  id: string;
  name: string;
  contactEmail: string;
  contactPersonName: string;
  contactPhone: string;
  phoneNumber: string;
  address: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
  };
  pan: string;
  gstin: string;
  description: string;
  isActive: boolean;
  registrationFee: number;
  createdAt: string;
  updatedAt: string;
  created_by: string;
  created_on: string;
  updated_by: string;
  updated_on: string;
  activeSubscription: {
    id: string;
    organizationId: string;
    planId: string;
    planName: string;
    validity: string;
    billingType: string;
    features: any;
    addOnFeatures: any;
    accessibleModules: string[];
    totalAmount: number;
    status: string;
    subscriptionType: string;
    isFreeTrial: boolean;
    trialUsed: boolean;
    userCreated: boolean;
    startDate: string;
    endDate: string;
    autoRenew: boolean;
    paymentMethod: string;
    paymentId: string | null;
    contactEmail: string;
    created_by: string;
    created_on: string;
    updated_on: string;
    updated_by: string;
  };
  subscriptionHistory: any[];
}> => {
  const response = await api.get<{
    id: string;
    name: string;
    contactEmail: string;
    contactPersonName: string;
    contactPhone: string;
    phoneNumber: string;
    address: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    pan: string;
    gstin: string;
    description: string;
    isActive: boolean;
    registrationFee: number;
    createdAt: string;
    updatedAt: string;
    created_by: string;
    created_on: string;
    updated_by: string;
    updated_on: string;
    activeSubscription: {
      id: string;
      organizationId: string;
      planId: string;
      planName: string;
      validity: string;
      billingType: string;
      features: any;
      addOnFeatures: any;
      accessibleModules: string[];
      totalAmount: number;
      status: string;
      subscriptionType: string;
      isFreeTrial: boolean;
      trialUsed: boolean;
      userCreated: boolean;
      startDate: string;
      endDate: string;
      autoRenew: boolean;
      paymentMethod: string;
      paymentId: string | null;
      contactEmail: string;
      created_by: string;
      created_on: string;
      updated_on: string;
      updated_by: string;
    };
    subscriptionHistory: any[];
  }>(`/subscription/v0.1/subscriber?id=${subscriberId}`);
  return response.data;
};
