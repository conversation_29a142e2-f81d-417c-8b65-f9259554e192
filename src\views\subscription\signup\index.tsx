'use client';

import { useState } from 'react';

import { PiSpinnerGapBold } from 'react-icons/pi';
import { toast } from 'sonner';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { useSubscriptionStore } from '@/store/subscription';

import { saveSubscriptionUserData } from '@/utils/subscription';
import { emailPattern } from '@/utils/validation';

import { routes } from '@/constants/routes';

import SignupTextField from '@/components/signup/SignupTextField';

const SignupView = () => {
  const router = useRouter();
  const { validateUserEmail } = useSubscriptionStore();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
  });
  const [errors, setErrors] = useState({
    name: '',
    email: '',
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateForm = () => {
    const newErrors = { name: '', email: '' };
    let isValid = true;

    if (!formData.name.trim()) {
      newErrors.name = 'Required';
      isValid = false;
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Required';
      isValid = false;
    } else {
      if (!emailPattern().value.test(formData.email)) {
        newErrors.email = emailPattern().message;
        isValid = false;
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSignUp = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Validate email before proceeding
      const emailValidationResult = await validateUserEmail(formData.email);

      if (!emailValidationResult.canProceed) {
        toast.error(emailValidationResult.message);
        setIsLoading(false);
        return;
      }

      // Store user data using utility function
      saveSubscriptionUserData({
        name: formData.name,
        email: formData.email,
        signupCompleted: true,
      });

      router.push(routes.SUBSCRIPTION_SELECT_ESTABLISHMENT);
    } catch (error) {
      console.error('Signup failed:', error);
      toast.error('Signup failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-6">
        <div>
          <SignupTextField
            label="Name"
            value={formData.name}
            onChange={(value) => handleInputChange('name', value)}
            required
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name}</p>
          )}
        </div>
        <div>
          <SignupTextField
            label="Email ID"
            type="email"
            value={formData.email}
            onChange={(value) => handleInputChange('email', value)}
            required
            showPasswordToggle={true}
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email}</p>
          )}
        </div>
      </div>

      <button
        onClick={handleSignUp}
        className="w-full flex justify-center items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <PiSpinnerGapBold className="animate-spin text-sm" />
            <span className="ml-2">Creating Account...</span>
          </>
        ) : (
          'Sign Up'
        )}
      </button>

      <div className="text-center">
        <p className="text-sm text-gray-600">
          Already have an account?{' '}
          <Link href={routes.LOGIN} className="font-medium ">
            Sign In
          </Link>
        </p>
      </div>
    </div>
  );
};

export default SignupView;
