import { type FC, memo, useMemo } from 'react';

import { SvgIcon } from '@mui/material';
import clsx from 'clsx';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { cn } from '@/lib/utils';

import colors from '@/utils/colors';

import AccessDeniedCard from '@/core/components/access-denied-card';
import LockedFeatureCard from '@/core/components/locked-feature-card';

import type { NavButtonProps } from './types';

const NavLink: FC<NavButtonProps> = ({
  icon,
  text,
  href,
  disabled = false,
  isLocked = false,
  showLocked = false,
  showAccessDenied = false,
  highlightColor = colors.common.navyBlue,
}) => {
  const pathname = usePathname();

  const isActive = useMemo(() => {
    // Special handling for profile routes to highlight parent menu
    if (href === '/emr/profile/personal-info') {
      return pathname.startsWith('/emr/profile');
    }
    return pathname.startsWith(href);
  }, [pathname, href]);

  const containerClass = useMemo(
    () =>
      clsx(
        'flex flex-col items-center justify-center flex-shrink-0 flex-grow-0',
        'gap-1 p-0.5 w-full h-16 rounded-base',
        'transition-all duration-100 ease-in-out',
        {
          'text-white': isActive && !disabled && !isLocked,
          'bg-gray-200 opacity-60 cursor-not-allowed pointer-events-none':
            disabled,
          '!opacity-100 mb-1': isLocked && showLocked,
        }
      ),
    [isActive, disabled, isLocked, showLocked]
  );

  const iconClass = useMemo(
    () =>
      cn('text-lg flex items-center justify-center w-6 h-6', {
        'text-primary': disabled || !isActive || (isLocked && showLocked),
        'text-white': isActive && !disabled && !isLocked,
      }),
    [disabled, isActive, isLocked, showLocked]
  );

  const textClass = useMemo(
    () =>
      cn(
        'text-[10px] -tracking-[2.2%] font-archivo text-center leading-tight w-full flex justify-center',
        {
          'text-primary': disabled || !isActive || (isLocked && showLocked),
          'text-white': isActive && !disabled && !isLocked,
        }
      ),
    [disabled, isActive, isLocked, showLocked]
  );

  // If show access denied, render AccessDeniedCard
  if (showAccessDenied) {
    return (
      <div className={containerClass}>
        <AccessDeniedCard
          text={text}
          textClassName={`${textClass} !text-black`}
          fullHeight={false}
          containerClassName={`${containerClass}`}
          horizontal={false}
        />
      </div>
    );
  }

  // If locked, render LockedFeatureCard
  if (isLocked && showLocked) {
    return (
      <div className={containerClass}>
        <LockedFeatureCard
          text={text}
          iconClassName={iconClass}
          textClassName={textClass}
          containerClassName="flex flex-col items-center justify-center w-full h-full"
          fullHeight={false}
          horizontal={false}
        />
      </div>
    );
  }

  return (
    <Link
      href={href}
      aria-disabled={disabled}
      className={containerClass}
      style={
        isActive && !disabled && !isLocked
          ? { backgroundColor: highlightColor }
          : undefined
      }
    >
      <span className={iconClass}>
        <SvgIcon color="inherit">{icon}</SvgIcon>
      </span>
      <span
        className={textClass}
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
        }}
      >
        {text}
      </span>
    </Link>
  );
};

export default memo(NavLink);
