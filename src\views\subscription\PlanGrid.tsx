'use client';

import { useState } from 'react';

// Custom arrow icons
const ChevronLeftIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15 19l-7-7 7-7"
    />
  </svg>
);

const ChevronRightIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 5l7 7-7 7"
    />
  </svg>
);

import PlanCard, { Plan } from './PlanCard';

interface PlanGridProps {
  plans: Plan[];
  billingCycle: 'monthly' | 'yearly';
  selectedPlan: string | null;
  onPlanSelect: (planId: string) => void;
  onGetQuote?: () => void;
  onSubscribe?: (planId: string) => void;
  isLoading?: boolean;
  plansPerPage?: number;
  onBillingCycleChange?: (cycle: 'monthly' | 'yearly') => void;
}

const PlanGrid = ({
  plans,
  billingCycle,
  selectedPlan,
  onPlanSelect,
  onGetQuote,
  onSubscribe,
  isLoading = false,
  plansPerPage = 4,
  onBillingCycleChange,
}: PlanGridProps) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const totalSlides = Math.ceil(plans.length / plansPerPage);
  const showNavigation = plans.length > plansPerPage;

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
  };

  const goToSlide = (slideIndex: number) => {
    setCurrentSlide(slideIndex);
  };

  const getCurrentPlans = () => {
    const startIndex = currentSlide * plansPerPage;
    const endIndex = startIndex + plansPerPage;
    return plans.slice(startIndex, endIndex);
  };

  return (
    <div className="relative">
      {/* Navigation Arrows */}
      {showNavigation && (
        <>
          {/* Left Arrow */}
          <button
            onClick={prevSlide}
            className="absolute left-[-50px] top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow duration-200 border border-gray-200"
            aria-label="Previous plans"
          >
            <ChevronLeftIcon className="w-6 h-6 text-gray-600" />
          </button>

          {/* Right Arrow */}
          <button
            onClick={nextSlide}
            className="absolute right-[-50px] top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow duration-200 border border-gray-200"
            aria-label="Next plans"
          >
            <ChevronRightIcon className="w-6 h-6 text-gray-600" />
          </button>
        </>
      )}

      {/* Plan Cards Grid */}
      <div className="grid md:grid-cols-4 gap-4 mb-4">
        {getCurrentPlans().map((plan) => (
          <PlanCard
            key={plan.id}
            plan={plan}
            billingCycle={billingCycle}
            selectedPlan={selectedPlan}
            onPlanSelect={onPlanSelect}
            onGetQuote={onGetQuote}
            onSubscribe={onSubscribe}
            isLoading={isLoading}
            allPlans={plans}
            onBillingCycleChange={onBillingCycleChange}
          />
        ))}
      </div>
    </div>
  );
};

export default PlanGrid;
