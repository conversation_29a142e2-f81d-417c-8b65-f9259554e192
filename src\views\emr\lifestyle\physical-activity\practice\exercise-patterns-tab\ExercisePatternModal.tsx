import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import type { MutableRefObject } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { toast } from 'sonner';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { exercisePatternStore } from '@/store/emr/lifestyle/physical-activity/practice/exercise-pattern-store';

import { detectAmbientInObject } from '@/utils/ambient-detection';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import LifestyleModalWrapper from '@/views/emr/lifestyle/shared/LifestyleModalWrapper';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import ExercisePatternForm from './ExercisePatternForm';

const hasFormData = (data: QuestionnaireResponse): boolean => {
  if (!data?.questions) return false;

  return data.questions.some((question) =>
    question.fields?.some((field) => {
      if (field.type === 'table' && field.value) {
        return (
          Array.isArray(field.value) &&
          field.value.length > 0 &&
          field.value.some((row) =>
            Object.values(row || {}).some(
              (value) => value !== null && value !== undefined && value !== ''
            )
          )
        );
      }

      return (
        field.value !== null && field.value !== undefined && field.value !== ''
      );
    })
  );
};

const getValidationMessage = (data: QuestionnaireResponse): string => {
  if (!data?.questions) return 'Please enter at least one record to save';

  const tableFields = data.questions.flatMap(
    (question) =>
      question.fields?.filter((field) => field.type === 'table') || []
  );

  if (tableFields.length === 0)
    return 'Please enter at least one record to save';

  const hasRows = tableFields.some(
    (field) => Array.isArray(field.value) && field.value.length > 0
  );

  if (!hasRows) {
    return 'Please enter at least one record to save';
  }

  return 'Please enter at least one record to save';
};

// Function to check if there are any completely empty rows
const hasEmptyRows = (data: QuestionnaireResponse): boolean => {
  if (!data?.questions) return false;

  return data.questions.some((question) =>
    question.fields?.some((field) => {
      if (field.type === 'table' && Array.isArray(field.value)) {
        return field.value.some((row) => {
          if (!row) return true; // null or undefined row is considered empty

          // Check if all values in the row are empty
          const values = Object.values(row);
          return (
            values.length > 0 &&
            values.every((value) => {
              if (Array.isArray(value)) {
                return value.length === 0;
              }
              return value === null || value === undefined || value === '';
            })
          );
        });
      }
      return false;
    })
  );
};

const ExercisePatternModal: React.FC<{
  patientData?: QuestionnaireResponse | null;
  mode?: LifestyleMode;
  onAfterSubmit?: () => void;
  hideSaveButton?: boolean;
  onSaveRef?: MutableRefObject<(() => void) | null>;
  initialValues?: any;
}> = ({
  patientData,
  mode = LifestyleMode.CREATE,
  onAfterSubmit,
  hideSaveButton = false,
  onSaveRef,
  initialValues,
}) => {
  const {
    getLifestyleQuestions,
    questions,
    questionLoading,
    updating,
    createLifestyleData,
    updateLifestyleData,
    patientData: storePatientData,
  } = exercisePatternStore();
  const { setModalOpen } = lifestyleStore();
  const profile = useDoctorStore((state) => state.doctorProfile);
  const { patient } = useCurrentPatientStore();

  const [currentMode, setCurrentMode] = useState(mode);

  const methods = useForm<QuestionnaireResponse>({
    defaultValues: patientData ?? initialValues ?? questions,
    mode: 'onChange',
  });

  const { watch, handleSubmit } = methods;

  const watchedData = watch();
  const isFormValid = useMemo(() => {
    if (currentMode === LifestyleMode.VIEW) {
      return true;
    }

    return hasFormData(watchedData);
  }, [watchedData, currentMode]);

  const formFields = useMemo(() => {
    if (!questions?.questions?.length) return [];

    return questions.questions;
  }, [questions]);

  const onSubmit = useCallback(
    async (data: QuestionnaireResponse) => {
      // Check if this is ambient listening data
      const isAmbientData = detectAmbientInObject(initialValues || data);

      // Ensure data has questions structure - if not, use questions template
      const formData = data?.questions
        ? data
        : {
            ...data,
            questions: questions?.questions || [],
          };

      // Check for empty rows first (skip for ambient data as it might be processed differently)
      if (
        !isAmbientData &&
        (currentMode === LifestyleMode.CREATE ||
          currentMode === LifestyleMode.EDIT) &&
        hasEmptyRows(formData)
      ) {
        toast.error("Empty row can't be saved", {
          style: {
            background: '#fef2f2',
            border: '1px solid #fecaca',
            color: '#dc2626',
            padding: '12px 16px',
            borderRadius: '6px',
          },
          closeButton: true,
          duration: 4000,
        });
        return;
      }

      // For ambient data, we'll validate after cleaning the data
      // For manual entry, validate before processing
      if (
        !isAmbientData &&
        (currentMode === LifestyleMode.CREATE ||
          currentMode === LifestyleMode.EDIT) &&
        !hasFormData(formData)
      ) {
        const validationMessage = getValidationMessage(formData);
        toast.error(validationMessage, {
          style: {
            background: '#fef2f2',
            border: '1px solid #fecaca',
            color: '#dc2626',
            padding: '12px 40px 12px 16px',
            borderRadius: '6px',
            position: 'relative',
          },
          closeButton: true,
          duration: 5000,
          className: 'custom-error-toast',
        });
        return;
      }

      try {
        const cleanedData = {
          ...formData,
          questions: formData.questions?.map((question) => ({
            ...question,
            fields: question.fields?.map((field) => {
              if (field.type === 'table' && Array.isArray(field.value)) {
                const filteredRows = field.value.filter((row) => {
                  if (!row) return false;

                  return Object.values(row).some((value) => {
                    if (Array.isArray(value)) {
                      return value.length > 0;
                    }
                    return (
                      value !== null && value !== undefined && value !== ''
                    );
                  });
                });
                return {
                  ...field,
                  value: filteredRows,
                };
              }
              return field;
            }),
          })),
        };

        // Validate data after cleaning - this applies to both ambient and manual data
        const hasValidData = cleanedData.questions?.some((question) =>
          question.fields?.some((field) => {
            if (field.type === 'table' && Array.isArray(field.value)) {
              return field.value.length > 0;
            }
            return (
              field.value !== null &&
              field.value !== undefined &&
              field.value !== ''
            );
          })
        );

        if (!hasValidData) {
          // For ambient data, provide a more helpful error message
          const errorMessage = isAmbientData
            ? 'No exercise pattern data found in the recording. Please add data manually or try recording again.'
            : 'Please enter at least one complete row before saving';
          toast.error(errorMessage, {
            style: {
              background: '#fef2f2',
              border: '1px solid #fecaca',
              color: '#dc2626',
              padding: '12px 16px',
              borderRadius: '6px',
            },
            duration: 4000,
          });
          return;
        }

        if (cleanedData?.id) {
          const updateData = {
            ...cleanedData,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
            },
          };
          await updateLifestyleData(updateData);
        } else {
          const createData = {
            ...cleanedData,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
            },
            // Include ambient listening data if available from initialValues
            ...(initialValues?.conversation && {
              conversation: initialValues.conversation,
            }),
            ...(initialValues?.recordingDuration && {
              recordingDuration: initialValues.recordingDuration,
            }),
          };
          await createLifestyleData(createData);
        }

        // Close modal immediately
        setModalOpen(false);

        // Call onAfterSubmit immediately - let it handle any cleanup needed
        onAfterSubmit?.();
      } catch (error) {
        console.error('Error submitting exercise pattern:', error);
        toast.error('Failed to save exercise pattern. Please try again.');
      }
    },
    [
      setModalOpen,
      updateLifestyleData,
      profile?.general?.fullName,
      profile?.general?.designation,
      profile?.id,
      createLifestyleData,
      onAfterSubmit,
      currentMode,
      initialValues,
      questions,
    ]
  );

  const handleSaveClick = useCallback(() => {
    // Ensure form is ready before submitting
    if (!questions?.questions?.length) {
      console.warn('Questions not loaded yet, cannot save');
      toast.error('Form is not ready. Please wait a moment and try again.');
      return;
    }

    handleSubmit(onSubmit)();
  }, [handleSubmit, onSubmit, questions]);

  useEffect(() => {
    if (onSaveRef) {
      onSaveRef.current = () => {
        try {
          // Double-check that form is ready
          if (!questions?.questions?.length) {
            console.warn('Save ref called but questions not loaded');
            toast.error(
              'Form is not ready. Please wait a moment and try again.'
            );
            return;
          }

          const currentValues = methods.getValues();

          // Check if this is ambient data - if so, we might need to bypass validation
          const isAmbientData = detectAmbientInObject(
            initialValues || currentValues
          );

          const submitFn = handleSubmit(
            (validData) => {
              onSubmit(validData);
            },
            (errors) => {
              console.error('Form validation failed:', errors);
              console.error(
                'Detailed validation errors:',
                JSON.stringify(errors, null, 2)
              );

              if (isAmbientData) {
                const formValues = methods.getValues();
                onSubmit(formValues as QuestionnaireResponse);
              } else {
                toast.error('Please fix the form errors before saving.');
              }
            }
          );
          submitFn();
        } catch (error) {
          console.error('Error in save ref function:', error);
          toast.error('An error occurred while saving. Please try again.');
        }
      };
    }
    return () => {
      if (onSaveRef) {
        onSaveRef.current = null;
      }
    };
  }, [onSaveRef, handleSubmit, onSubmit, questions, methods, initialValues]);

  useEffect(() => {
    getLifestyleQuestions();
  }, [getLifestyleQuestions]);

  useEffect(() => {
    if (patientData) {
      methods.reset(patientData);
    } else if (initialValues) {
      if (questions?.questions && initialValues?.questions) {
        const mergedData = {
          ...initialValues,
          questions: questions.questions.map(
            (questionGroup: any, index: number) => {
              const ambientGroup = initialValues.questions?.[index];
              if (!ambientGroup) {
                return questionGroup;
              }

              return {
                ...questionGroup,
                fields: questionGroup.fields?.map(
                  (field: any, fieldIndex: number) => {
                    const ambientField = ambientGroup.fields?.[fieldIndex];
                    if (!ambientField) {
                      return field;
                    }

                    if (field.type === 'table' && ambientField.value) {
                      return {
                        ...field,
                        value: ambientField.value,
                      };
                    }

                    return {
                      ...field,
                      value:
                        ambientField.value !== undefined
                          ? ambientField.value
                          : field.value,
                    };
                  }
                ),
              };
            }
          ),
        };

        const formData: any = {};
        mergedData.questions?.forEach(
          (questionGroup: any, groupIndex: number) => {
            if (!formData.questions) formData.questions = [];
            if (!formData.questions[groupIndex])
              formData.questions[groupIndex] = { fields: [] };

            questionGroup.fields?.forEach((field: any, fieldIndex: number) => {
              if (field.value !== undefined) {
                if (!formData.questions[groupIndex].fields[fieldIndex]) {
                  formData.questions[groupIndex].fields[fieldIndex] = {};
                }
                formData.questions[groupIndex].fields[fieldIndex].value =
                  field.value;
              }
            });
          }
        );

        methods.reset({ ...mergedData, ...formData });
      } else {
        methods.reset(initialValues);
      }
    } else if (questions) {
      methods.reset(questions);
    }
  }, [patientData, initialValues, questions, methods]);

  return (
    <FormProvider {...methods}>
      <LifestyleModalWrapper
        loading={questionLoading}
        onSubmit={handleSaveClick}
        updating={updating}
        mode={currentMode}
        onEdit={() => setCurrentMode(LifestyleMode.EDIT)}
        finalized={patientData?.status === LifestyleRecordStatus.FINALIZED}
        hideSaveButton={hideSaveButton}
        isFormValid={isFormValid}
        data={patientData || undefined}
        doctorName={profile?.general?.fullName}
        patientName={patient?.name}
      >
        <ExercisePatternForm
          formFields={formFields}
          readonly={currentMode === LifestyleMode.VIEW}
          showHeading={true}
          mode={currentMode}
          patientData={storePatientData}
          variant="modal"
          isAmbientForm={detectAmbientInObject(initialValues || patientData)}
        />
      </LifestyleModalWrapper>
    </FormProvider>
  );
};

export default memo(ExercisePatternModal);
