﻿// Server-only instrumentation file
// This file contains the actual telemetry setup logic

export async function initializeTelemetry() {
  const serviceName = process.env.SERVICE_NAME || 'arca-emr';

  // Detect environment based on available configuration
  const isVercel = !!process.env.VERCEL;
  const isAzure = !!process.env.APPLICATIONINSIGHTS_CONNECTION_STRING;
  const hasGrafanaConfig = !!(
    process.env.GRAFANA_OTLP_ENDPOINT &&
    process.env.GRAFANA_USER &&
    process.env.GRAFANA_API_KEY
  );

  let traceExporter: any;
  let telemetryProvider: string;

  try {
    // Priority 1: Azure Application Insights (Production)
    if (isAzure) {
      const connectionString =
        process.env.APPLICATIONINSIGHTS_CONNECTION_STRING;

      telemetryProvider = 'Azure Application Insights';

      // Dynamic import to avoid webpack issues
      const { AzureMonitorTraceExporter } = await import(
        /* webpackIgnore: true */ '@azure/monitor-opentelemetry-exporter'
      );
      traceExporter = new AzureMonitorTraceExporter({
        connectionString,
      });
    }
    // Priority 2: Grafana Cloud OTLP (QA/Staging)
    else if (hasGrafanaConfig) {
      const otlpEndpoint = process.env.GRAFANA_OTLP_ENDPOINT;
      const grafanaUser = process.env.GRAFANA_USER;
      const grafanaApiKey = process.env.GRAFANA_API_KEY;

      telemetryProvider = 'Grafana Cloud';

      // Dynamic import to avoid webpack issues
      const { OTLPTraceExporter } = await import(
        /* webpackIgnore: true */ '@opentelemetry/exporter-trace-otlp-http'
      );
      traceExporter = new OTLPTraceExporter({
        url: otlpEndpoint,
        headers: {
          Authorization: `Basic ${btoa(`${grafanaUser}:${grafanaApiKey}`)}`,
          'Content-Type': 'application/json',
        },
      });
    }
    // No telemetry configuration found
    else {
      return;
    }

    // Register OpenTelemetry with the appropriate exporter
    const { registerOTel } = await import('@vercel/otel');
    registerOTel({
      serviceName,
      traceExporter,
    });
  } catch (error) {
    console.error('❌ Failed to initialize OpenTelemetry:', error);
    // Don't throw - we don't want telemetry issues to break the app
  }
}
