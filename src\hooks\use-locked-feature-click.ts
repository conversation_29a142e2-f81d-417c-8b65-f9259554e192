import { useRouter } from 'next/navigation';

import { useUserStore } from '@/store/userStore';

import { PERMISSION_KEYS } from '@/constants/permission-keys';
import { routes } from '@/constants/routes';

export const useLockedFeatureClick = () => {
  const router = useRouter();
  const { data, permissions = [] } = useUserStore();

  const hasEMRAccess = permissions.includes(PERMISSION_KEYS.EMR_ACCESS);
  const isClinicAccount = data?.accountType === 'clinic';

  const isClickable = hasEMRAccess && isClinicAccount;

  const tooltipMessage = !hasEMRAccess
    ? 'Feature locked, no EMR access to upgrade'
    : !isClinicAccount
      ? 'Feature locked, contact admin to unlock'
      : 'Feature Locked';

  const handleLockedFeatureClick = () => {
    if (isClickable) {
      // Only redirect for clinic accounts with EMR access
      router.push(routes.EMR_PROFILE_PAYMENT_PLANS);
    }
  };

  return {
    handleLockedFeatureClick,
    isClickable,
    tooltipMessage,
    isClinicAccount,
  };
};
