import React, { memo, useMemo } from 'react';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useLabPrintStore } from '@/store/emr/lab/print-store';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

const PatientDetails = () => {
  const { patient } = useCurrentPatientStore();
  const { printItems } = useLabPrintStore();

  const patientDetails = useMemo(
    () => [
      {
        label: 'Patient Name',
        value: `${patient?.name} (${patient?.sex?.slice(0, 1)})`,
      },
      {
        label: 'Patient ID',
        value: patient?.id,
      },
      { label: 'Age', value: patient?.age },
      { label: 'Mobile', value: patient?.contact?.phone },
      {
        label: 'Date',
        value: formatDate(
          printItems[0]?.updated_on || new Date().toISOString(),
          DateFormats.DATE_DD_MM_YYYY_SLASH
        ),
      },
    ],
    [
      patient?.age,
      patient?.contact?.phone,
      patient?.id,
      patient?.name,
      patient?.sex,
      printItems,
    ]
  );

  return (
    <div className="w-full pb-base">
      <div className="text-sm" style={{ marginRight: '30px' }}>
        <div className="grid grid-cols-5 gap-12 mb-1">
          <div className="font-normal text-black-600">Patient Name:</div>
          <div className="font-normal text-black-600">Patient ID:</div>
          <div className="font-normal text-black-600">Age:</div>
          <div className="font-normal text-black-600">Mobile:</div>
          <div className="font-normal text-black-600 ">Date:</div>
        </div>
        <div className="grid grid-cols-5 gap-12">
          <div className="font-normal text-black">
            {patientDetails[0]?.value}
          </div>
          <div className="font-normal text-black">
            {patientDetails[1]?.value}
          </div>
          <div className="font-normal text-black">
            {patientDetails[2]?.value}
          </div>
          <div className="font-normal text-black">
            {patientDetails[3]?.value}
          </div>
          <div className="font-normal text-black ">
            {patientDetails[4]?.value}
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(PatientDetails);
