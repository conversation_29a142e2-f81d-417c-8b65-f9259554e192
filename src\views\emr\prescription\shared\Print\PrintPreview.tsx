import React, { useEffect } from 'react';

import { Box, Typography } from '@mui/material';

// Store imports
import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useCustomiseEmrStore } from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { usePrescriptionStore } from '@/store/emr/prescription';
import { useOrganizationStore } from '@/store/organizationStore';
import { useUserStore } from '@/store/userStore';

// Utils and templates
import { printHtmlContent } from '@/utils/print/printUtils';
import {
  getPrescriptionHtml,
  prescriptionStyles,
} from '@/utils/print/templates/prescription-template';

// Components
import PrintModal from '@/views/shared/print/PrintModal';

// Types and helpers
import { PatientI } from '@/types';

import { MedicineTable } from './MedicineTable';
import {
  calculateTotalAmount,
  formatOrganization,
  formatPatientData,
} from './utils';

// Types
interface PatientData {
  id?: string;
  name?: string;
  age?: string | number;
  mobile?: string;
  gender?: string;
}

interface PrintModalProps {
  open: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  patient?: PatientI;
}

interface PrescriptionData {
  id: string;
  doctor: any;
  patient: PatientData;
  organization: any;
  medicines: any[];
  created_on: string;
}

const useOrganizationData = (open: boolean, organizationId?: string) => {
  const { organization, isLoading, error, fetchOrganization } =
    useOrganizationStore();

  useEffect(() => {
    if (open && organizationId) {
      fetchOrganization(organizationId);
    }
  }, [open, organizationId, fetchOrganization]);

  return { organization, isLoading, error };
};

const SignatureSection: React.FC<{
  totalAmount: number;
  digitalSignature?: string;
}> = ({ totalAmount, digitalSignature }) => (
  <Box display="flex" flexDirection={'column'} mb={2} px={2}>
    <Box
      width={100}
      height={41}
      display="flex"
      alignItems="center"
      justifyContent="center"
      mb={1}
    >
      {digitalSignature ? (
        <img
          src={digitalSignature}
          alt="Digital Signature"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
          }}
        />
      ) : (
        <Box
          bgcolor="#C2CDD6"
          width="100%"
          height="100%"
          display="flex"
          alignItems="center"
          justifyContent="center"
        >
          <Typography variant="caption" color="text.secondary">
            Signature
          </Typography>
        </Box>
      )}
    </Box>

    <Box
      textAlign="right"
      sx={{
        py: 1,
        borderTop: '1px solid #e0e0e0',
      }}
    >
      <Typography sx={{ fontSize: '14px', fontWeight: 'bold', mt: 1 }}>
        Total Amount: ₹{totalAmount.toFixed(2)}
      </Typography>
    </Box>
  </Box>
);

const ReturnPolicySection: React.FC = () => (
  <Box borderTop="1px solid #e0e0e0" sx={{ backgroundColor: '#f8f9fa' }}>
    <Typography
      variant="caption"
      display="block"
      textAlign="center"
      color="textSecondary"
      sx={{ fontSize: '12px' }}
      mt={1}
    >
      Medicines returned after 15 days won&apos;t be taken back.
    </Typography>
  </Box>
);

const PrintPreview: React.FC<PrintModalProps> = ({ open, onCancel }) => {
  const { selectedHistory } = usePrescriptionStore();
  const { patient } = useCurrentPatientStore();
  const { data: userData } = useUserStore();
  const { customiseEmrData, fetchCustomiseEmr } = useCustomiseEmrStore();
  const { doctorProfile } = useDoctorStore();

  const { organization } = useOrganizationData(open, userData?.organizationId);

  useEffect(() => {
    if (doctorProfile?.id) {
      fetchCustomiseEmr(doctorProfile.id);
    }
  }, [doctorProfile?.id, fetchCustomiseEmr]);

  // Get the most recent customise EMR data
  const mostRecentData = customiseEmrData?.length
    ? [...customiseEmrData].sort(
        (a, b) =>
          new Date(b.updated_on || '').getTime() -
          new Date(a.updated_on || '').getTime()
      )[0]
    : null;

  const totalAmount = calculateTotalAmount(selectedHistory?.medicines || []);
  const patientData = formatPatientData(
    patient || undefined,
    selectedHistory || {}
  );
  const formattedOrganization = formatOrganization(organization);

  const handlePrint = () => {
    if (!selectedHistory) return;

    const prescriptionData: PrescriptionData = {
      id: selectedHistory.id || '',
      doctor: {
        name: selectedHistory?.doctor,
      },
      patient: patientData,
      organization: formattedOrganization,
      medicines: selectedHistory.medicines || [],
      created_on: selectedHistory.created_on || new Date().toISOString(),
    };

    const content = getPrescriptionHtml(
      prescriptionData,
      totalAmount,
      1,
      1,
      mostRecentData?.digitalSignature || '',
      mostRecentData?.organizationLogo || '',
      mostRecentData?.letterHeadDetails || ''
    );

    printHtmlContent(content, {
      styles: prescriptionStyles,
      onAfterPrint: onCancel,
    });
  };

  if (!selectedHistory) {
    return (
      <PrintModal
        open={open}
        onClose={onCancel}
        onPrint={handlePrint}
        patient={patientData}
        title="Prescription Print Preview"
        prescriptionTitle="Prescription"
        organization={formattedOrganization}
      >
        <Box p={2}>
          <Typography>No prescription data available</Typography>
        </Box>
      </PrintModal>
    );
  }

  return (
    <PrintModal
      open={open}
      onClose={onCancel}
      onPrint={handlePrint}
      patient={patientData}
      title="Prescription Print Preview"
      prescriptionTitle="Prescription"
      organization={formattedOrganization}
      doctor={selectedHistory?.doctor}
    >
      <MedicineTable medicines={selectedHistory.medicines || []} />
      <Box
        sx={{
          mt: 2,
          backgroundColor: 'white',
          '& *': { color: '#012436' },
        }}
      >
        <SignatureSection
          totalAmount={totalAmount}
          digitalSignature={mostRecentData?.digitalSignature}
        />
        <ReturnPolicySection />
      </Box>
    </PrintModal>
  );
};

export default PrintPreview;
