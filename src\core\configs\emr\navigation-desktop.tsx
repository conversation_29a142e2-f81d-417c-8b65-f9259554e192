import ConsultationIcon from '@/assets/svg/consultation-icon.svg';
import LifestyleIcon from '@/assets/svg/lifestyle-icon.svg';
import PatientInfo from '@/assets/svg/patient-info-icon.svg';
import PrescriptionIcon from '@/assets/svg/prescription-icon.svg';
import ReportsIcon from '@/assets/svg/reports-icon.svg';

import { mapObjectToArray } from '@/helpers/utils';

import departments from '@/constants/departments';
import { PERMISSION_KEYS } from '@/constants/permission-keys';
import { routes } from '@/constants/routes';

import { SidebarItem } from '@/core/layout/shared/side-bar/types';

const { lifestyle } = departments;
const {
  EMR_PATIENT_INFO,
  EMR_CONSULTATION,
  EMR_REPORTS,
  EMR_LIFE_STYLE,
  EMR_PRESCRIPTION,
  EMR_DASHBOARD,
} = routes;

const emrNavigation: SidebarItem[] = [
  // {
  //   label: 'Dashboard',
  //   path: EMR_DASHBOARD,
  //   icon: <DashboardIcon />,
  //   module: 'emr',
  //   permissions: [
  //     PERMISSION_KEYS.EMR_DASHBOARD_VIEW,
  //     PERMISSION_KEYS.EMR_ACCESS,
  //   ],
  //   requireAll: true,
  // },
  {
    label: 'Patient Info',
    path: EMR_PATIENT_INFO,
    icon: <PatientInfo />,
    module: 'emr',
    permissions: [
      PERMISSION_KEYS.EMR_PATIENT_INFO_VIEW,
      PERMISSION_KEYS.EMR_PATIENT_INFO_EDIT,
      PERMISSION_KEYS.EMR_ACCESS,
      PERMISSION_KEYS.MRD_PATIENT_QUEUE_MANAGE,
    ],
    requireAll: true,
  },
  {
    label: 'Consulting',
    path: EMR_CONSULTATION,
    icon: <ConsultationIcon />,
    module: 'emr',
    permissions: [
      PERMISSION_KEYS.EMR_CONSULTATION_MANAGE,
      PERMISSION_KEYS.EMR_CONSULTATION_VIEW,
      PERMISSION_KEYS.EMR_CONSULTATION_CREATE,
      PERMISSION_KEYS.EMR_CONSULTATION_EDIT,
      PERMISSION_KEYS.EMR_ACCESS,
    ],
    requireAll: true,
  },
  {
    label: 'Labs',
    path: EMR_REPORTS,
    icon: <ReportsIcon />,
    module: 'emr',
    permissions: [
      PERMISSION_KEYS.EMR_LAB_TEST_VIEW,
      PERMISSION_KEYS.EMR_LAB_TEST_MANAGE,
      PERMISSION_KEYS.EMR_LAB_TEST_SEARCH,
      PERMISSION_KEYS.EMR_ACCESS,
    ],
    requireAll: true,
  },
  {
    label: 'Prescriptions',
    path: EMR_PRESCRIPTION,
    icon: <PrescriptionIcon />,
    module: 'emr',
    permissions: [
      PERMISSION_KEYS.EMR_PRESCRIPTION_VIEW,
      PERMISSION_KEYS.EMR_PRESCRIPTION_MANAGE,
      PERMISSION_KEYS.EMR_ACCESS,
    ],
    requireAll: true,
  },

  {
    label: 'Lifestyle',
    path: EMR_LIFE_STYLE,
    icon: <LifestyleIcon />,
    module: 'emr',
    permissions: [PERMISSION_KEYS.EMR_LIFESTYLE, PERMISSION_KEYS.EMR_ACCESS],
    requireAll: true,
    department: mapObjectToArray(lifestyle),
  },
];

export default emrNavigation;
