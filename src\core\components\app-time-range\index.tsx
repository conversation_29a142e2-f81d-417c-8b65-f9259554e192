import React from 'react';

import { Box } from '@mui/material';

import AppTimePicker from '@/core/components/app-time-picker';

import { AppTimeRangeProps } from './types';

const AppTimeRange: React.FC<AppTimeRangeProps> = ({
  onChange,
  disabled = false,
  value,
  ...rest
}) => {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <AppTimePicker
        value={value?.from ?? ''}
        onChange={(from) => {
          onChange && onChange({ from, to: value?.to ?? '' });
        }}
        disabled={disabled}
        {...rest}
      />
      <Box sx={{ color: '#6b7280', fontSize: '14px' }}>to</Box>
      <AppTimePicker
        value={value?.to ?? ''}
        onChange={(to) => {
          onChange && onChange({ to, from: value?.from ?? '' });
        }}
        disabled={disabled}
        {...rest}
      />
    </Box>
  );
};

export default AppTimeRange;
