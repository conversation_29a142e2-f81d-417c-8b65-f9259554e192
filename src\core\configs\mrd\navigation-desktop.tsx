// import DashboardIcon from '@/assets/svg/dashboard-icon.svg';
import ManagePatientsIcon from '@/assets/svg/manage-patients-icon.svg';
// import MessageIcon from '@/assets/svg/message-icon.svg';

import { PERMISSION_KEYS } from '@/constants/permission-keys';
import { routes } from '@/constants/routes';

import { SidebarItem } from '@/core/layout/shared/side-bar/types';

const {
  MRD_DASHBOARD,
  MRD_MANAGE_PATIENTS,
  // MRD_MESSAGES
} = routes;

const mrdNavigation: SidebarItem[] = [
  // {
  //   label: 'Dashboard',
  //   path: MRD_DASHBOARD,
  //   icon: <DashboardIcon />,
  //   module: 'mrd',
  //   permissions: [PERMISSION_KEYS.MRD_DASHBOARD_VIEW],
  // },
  {
    label: 'Manage Patients',
    path: MRD_MANAGE_PATIENTS,
    icon: <ManagePatientsIcon />,
    module: 'mrd',
    permissions: [
      PERMISSION_KEYS.MRD_MANAGE_PATIENT_VIEW,
      PERMISSION_KEYS.MRD_MANAGE_PATIENT_EDIT,
      PERMISSION_KEYS.MRD_PATIENT_QUEUE_MANAGE,
      PERMISSION_KEYS.MRD_VITALS_VIEW,
      PERMISSION_KEYS.MRD_VITALS_MANAGE,
      PERMISSION_KEYS.MRD_CONSULTATION_BOOK,
    ],
  },
  // {
  //   label: 'Messages',
  //   path: MRD_MESSAGES,
  //   icon: <MessageIcon />,
  //   disabled: true,
  // },
];

export default mrdNavigation;
