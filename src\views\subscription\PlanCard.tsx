'use client';

import { useState, useEffect } from 'react';

import { toast } from 'sonner';

import { usePaymentStore } from '@/store/payments';
import { useSubscriptionStore } from '@/store/subscription';
import { useUserStore } from '@/store/userStore';

import { startTrial } from '@/query/subscription';

import { renderHTMLContent } from '@/utils/renderHTMLContent';
import { Feature, saveSubscriptionUserData } from '@/utils/subscription';

import GreenCheckIcon from '@/assets/icons/GreenCheckIcon';

import AddFeaturesModal from './components/AddFeaturesModal';
import FreeTrialSuccessModal from './components/FreeTrialSuccessModal';
import PaymentFailureModal from './components/PaymentFailureModal';
import PaymentSuccessModal from './components/PaymentSuccessModal';
import SubscriptionDetailsModal from './components/SubscriptionDetailsModal';
import UpgradeModal from './components/UpgradeModal';

// Helper function to check if add-on features are actually available
const hasAddOnFeatures = (plan: Plan): boolean => {
  if (!plan.apiPlan?.addOnFeatures) return false;

  const { MRD, EMR, Billing } = plan.apiPlan.addOnFeatures;
  return (
    (MRD?.length || 0) > 0 ||
    (EMR?.length || 0) > 0 ||
    (Billing?.length || 0) > 0
  );
};

export interface Plan {
  id: string;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  subtitle: string;
  features: string[];
  popular?: boolean;
  buttonText: string;
  buttonColor: string;
  savings?: string;
  hasAddOnFeatures?: boolean;
  isOrganizationPlan?: boolean;
  apiPlan?: any; // Store original API plan data
}

interface PlanCardProps {
  plan: Plan;
  billingCycle: 'monthly' | 'yearly';
  selectedPlan: string | null;
  onPlanSelect: (planId: string) => void;
  onGetQuote?: () => void;
  onSubscribe?: (planId: string) => void;
  isLoading?: boolean;
  allPlans?: Plan[];
  isFromProfile?: boolean;
  currentPlanId?: string;
  onAddFeaturesClick?: (plan?: any) => void;
  onBillingCycleChange?: (cycle: 'monthly' | 'yearly') => void;
}

const PlanCard = ({
  plan,
  billingCycle,
  selectedPlan,
  onPlanSelect,
  onGetQuote,
  onSubscribe,
  isLoading = false,
  allPlans = [],
  isFromProfile = false,
  currentPlanId,
  onAddFeaturesClick,
  onBillingCycleChange,
}: PlanCardProps) => {
  const [showModal, setShowModal] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [showAddFeaturesModal, setShowAddFeaturesModal] = useState(false);
  const [billingDetails, setBillingDetails] = useState({
    name: '',
    email: '',
    promoCode: '',
  });
  const [addedFeatures, setAddedFeatures] = useState<Feature[]>([]);
  const [showPaymentSuccessModal, setShowPaymentSuccessModal] = useState(false);
  const [showPaymentFailureModal, setShowPaymentFailureModal] = useState(false);
  const [trialErrorMessage, setTrialErrorMessage] = useState<
    string | React.ReactElement
  >('');
  const [isTrialLoading, setIsTrialLoading] = useState(false);
  const [showFreeTrialSuccessModal, setShowFreeTrialSuccessModal] =
    useState(false);
  const [addFeaturesPlan, setAddFeaturesPlan] = useState<any>(plan);

  const { openRazorpayPayment } = usePaymentStore();

  // Get user data from user store
  const { data: userStoreData } = useUserStore();

  // Get subscriber data from subscription store for prorated billing
  const { subscriberData } = useSubscriptionStore();

  // Auto-fill billing details when from profile
  useEffect(() => {
    if (isFromProfile && userStoreData) {
      setBillingDetails({
        name: userStoreData.name || userStoreData.fullName || '',
        email: userStoreData.email || '',
        promoCode: '',
      });
    }
  }, [isFromProfile, userStoreData]);

  const currentPrice =
    billingCycle === 'monthly' ? plan.monthlyPrice : plan.yearlyPrice;

  // Get next plan for upgrade based on price
  const getNextPlan = () => {
    if (!allPlans || allPlans.length === 0) return null;

    // Sort plans by monthly price (or yearly price if monthly is 0)
    const sortedPlans = allPlans
      .filter((p) => p.monthlyPrice > 0 || p.yearlyPrice > 0) // Exclude free plans
      .sort((a, b) => {
        const priceA = a.monthlyPrice > 0 ? a.monthlyPrice : a.yearlyPrice;
        const priceB = b.monthlyPrice > 0 ? b.monthlyPrice : b.yearlyPrice;
        return priceA - priceB;
      });

    // Find current plan's position
    const currentIndex = sortedPlans.findIndex((p) => p.id === plan.id);

    // Return next plan if exists
    if (currentIndex >= 0 && currentIndex < sortedPlans.length - 1) {
      return sortedPlans[currentIndex + 1];
    }

    return null;
  };

  const nextPlan = getNextPlan();

  const handleCardClick = () => {
    onPlanSelect(plan.id);
  };

  const handleButtonClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    // For organization plans or Get Quote buttons, navigate to custom pricing
    if (plan.isOrganizationPlan || buttonText === 'Get Quote') {
      onGetQuote?.();
    } else {
      // Show subscription modal for other plans
      onPlanSelect(plan.id);
      setShowModal(true);
    }
  };

  const handleModalClose = () => {
    setShowModal(false);
    setShowUpgradeModal(false);
    setShowAddFeaturesModal(false);
    setBillingDetails({ name: '', email: '', promoCode: '' });
  };

  const handleProceed = async () => {
    // If it's a free trial, show free trial success modal
    if (plan.id === 'free-trial') {
      handleModalClose();
      setShowFreeTrialSuccessModal(true);
      return;
    }

    try {
      // Calculate total amount including added features
      const featuresTotal = addedFeatures.reduce(
        (sum, feature) =>
          sum + (feature.monthlyAmount || feature.yearlyAmount || 0),
        0
      );
      const totalAmount = currentPrice + featuresTotal;

      // Check if it's a free plan (cost is 0 and not organization plan)
      if (totalAmount === 0 && !plan.isOrganizationPlan) {
        // Set trial loading state - show processing in modal
        setIsTrialLoading(true);

        // Get user data from subscription storage (role info from role page)
        const userData =
          typeof window !== 'undefined'
            ? JSON.parse(localStorage.getItem('subscription_user_data') || '{}')
            : {};

        // Use billing details or fallback to user store data or signup data
        const finalEmail =
          billingDetails.email || userStoreData?.email || userData.email || '';
        const finalName =
          billingDetails.name ||
          userStoreData?.name ||
          userStoreData?.fullName ||
          userData.name ||
          '';

        // Get roleId - prioritize userStoreData when from profile
        const roleId = isFromProfile ? userStoreData?.roleId : userData.roleId;

        // Validate required fields - Don't close modal, show error in modal
        if (!finalEmail || !finalName) {
          setIsTrialLoading(false);
          setTrialErrorMessage(
            'Please provide both name and email to start the trial.'
          );
          setShowPaymentFailureModal(true);
          return;
        }

        // Validate role selection
        if (!roleId) {
          setIsTrialLoading(false);
          setTrialErrorMessage('Please select a role to start the trial.');
          setShowPaymentFailureModal(true);
          return;
        }

        // Prepare API payload with role information
        const trialData = {
          email: finalEmail,
          name: finalName,
          phoneNumber: null,
          planId: plan.id,
          billingType: billingCycle,
          roleId: roleId,
          userType: userStoreData?.role || userData.role || 'admin',
          userRole: userStoreData?.role || userData.role || 'admin',
          Billing: [
            {
              name: finalName,
              email: finalEmail,
              pincode: billingDetails.promoCode || '',
            },
          ],
        };

        // Keep modal open during processing - don't close it yet
        const result = await startTrial(trialData);
        setIsTrialLoading(false);

        if (result.success) {
          // Store trial data
          saveSubscriptionUserData({
            selectedPlan: plan.id,
            subscriptionCompleted: true,
          });

          // Close modal and show success
          handleModalClose();
          setShowPaymentSuccessModal(true);
          onSubscribe?.(plan.id);
        } else {
          // Check if the error message contains active subscription info
          const errorMessage =
            result.message || 'Failed to start trial. Please try again.';
          const isActiveSubscriptionError =
            errorMessage
              .toLowerCase()
              .includes('already have an active subscription') ||
            errorMessage.toLowerCase().includes('active subscription');

          if (isActiveSubscriptionError) {
            // Show error with additional info about active subscription
            setTrialErrorMessage(
              <div>
                <div className="mb-2">{errorMessage}</div>
                <div className="text-sm text-gray-600">
                  You already have an active subscription. To change your plan,
                  please upgrade or modify it from within the application.
                </div>
              </div>
            );
          } else {
            // Show actual error message from API in error modal (remove double quotes)
            setTrialErrorMessage(errorMessage.replace(/^"(.*)"$/, '$1'));
          }
          setShowPaymentFailureModal(true);
          // Keep subscription details modal open - don't close it
        }
      } else {
        // For paid plans, proceed with Razorpay payment
        const mockOrderData = {
          success: true,
          data: {
            orderId: `order_${Date.now()}`,
            paymentId: `pay_${Date.now()}`,
            keyId: 'rzp_test_key',
            amount: totalAmount * 100, // Razorpay expects amount in paise
            currency: 'INR',
            status: 'created',
            description: `${plan.name} Subscription`,
          },
        };

        // Open Razorpay payment with mock data
        await openRazorpayPayment(
          mockOrderData,
          billingDetails.name || 'User',
          async (response) => {
            // Payment successful
            setShowPaymentSuccessModal(true);
            onSubscribe?.(plan.id);
          },
          (error) => {
            // Payment failed
            console.error('Payment failed:', error);
            setShowPaymentFailureModal(true);
          }
        );
      }
    } catch (error: any) {
      console.error('Error processing payment/trial:', error);

      // For trial errors, set loading false
      setIsTrialLoading(false);

      // Show actual error message if available, handle HTTP errors properly
      const errorMessage =
        error?.response?.data?.message ||
        (error?.response?.data ? JSON.stringify(error.response.data) : null) ||
        error?.message ||
        `Request failed with status code ${error?.response?.status || 'Unknown'}`;

      // Calculate amount to determine if it's a free plan
      const featuresTotal = addedFeatures.reduce(
        (sum, feature) =>
          sum + (feature.monthlyAmount || feature.yearlyAmount || 0),
        0
      );
      const amount = currentPrice + featuresTotal;

      // For free plans (trial), show error modal instead of toast
      if (amount === 0 && !plan.isOrganizationPlan) {
        // Remove double quotes from error message
        setTrialErrorMessage(errorMessage.replace(/^"(.*)"$/, '$1'));
        setShowPaymentFailureModal(true);
        // Keep subscription details modal open - don't close it
      } else {
        // For paid plans, close modal and show error
        handleModalClose();
        // Remove double quotes from error message for toast
        toast.error(errorMessage.replace(/^"(.*)"$/, '$1'));
        setShowPaymentFailureModal(true);
      }
    }
  };

  const handleUpgradeClick = () => {
    // For upgrade from free plans, directly proceed with upgrade flow
    if (plan.monthlyPrice === 0 || plan.yearlyPrice === 0) {
      // This is an upgrade from free plan, proceed directly
      handleProceed();
    } else {
      setShowUpgradeModal(true);
    }
  };

  const handleAddFeaturesClick = (planOverride?: any) => {
    // Use the planOverride if provided, otherwise use the default plan
    setAddFeaturesPlan(planOverride || plan);
    setShowAddFeaturesModal(true);
  };

  const handleFeatureToggle = (feature: Feature) => {
    setAddedFeatures((prev) => {
      const exists = prev.find((f) => f.featureId === feature.featureId);
      if (exists) {
        return prev.filter((f) => f.featureId !== feature.featureId);
      } else {
        return [...prev, feature];
      }
    });
  };

  const calculatePricing = () => {
    const premiumAmount = currentPrice;
    const featuresAmount = addedFeatures.reduce(
      (sum, feature) =>
        sum + (feature.monthlyAmount || feature.yearlyAmount || 0),
      0
    );
    const subTotal = premiumAmount + featuresAmount;
    const tax = Math.round(subTotal * 0.18); // 18% tax
    const totalAmount = subTotal + tax;

    return {
      premiumAmount,
      featuresAmount,
      subTotal,
      tax,
      totalAmount,
    };
  };

  const pricing = calculatePricing();

  // Modify button text for profile page
  let buttonText = plan.buttonText;
  if (
    isFromProfile &&
    currentPlanId &&
    plan.id === currentPlanId &&
    !plan.isOrganizationPlan
  ) {
    buttonText = 'Upgrade';
  }

  return (
    <>
      <div
        className={`relative bg-white rounded-lg border-2 p-6 cursor-pointer transition-all duration-200 ${
          isFromProfile ? 'h-[60vh]' : 'h-[65vh]'
        } flex flex-col border-gray-200 hover:bg-[#012436] hover:text-white group ${
          plan.isOrganizationPlan ? 'group-hover:text-white' : ''
        }`}
        onClick={handleCardClick}
      >
        <div className="flex-1 overflow-y-auto mb-2">
          {/* Plan header - Left aligned */}
          <div className="text-left mb-6">
            <h3
              className={`text-xl font-semibold mb-2 ${plan.isOrganizationPlan ? 'text-gray-700 group-hover:text-white' : 'group-hover:text-white'}`}
            >
              {plan.name}
            </h3>
            <p
              className={`text-sm mb-4 ${plan.isOrganizationPlan ? 'text-gray-600 group-hover:text-gray-300' : 'text-gray-600 group-hover:text-gray-300'}`}
            >
              {plan.subtitle}
              {isFromProfile &&
                currentPlanId &&
                plan.id === currentPlanId &&
                subscriberData?.activeSubscription?.billingType ===
                  billingCycle && (
                  <span className="bg-green-100 text-green-800 text-xs font-medium px-2 ms-4 py-1 rounded-full">
                    Current Plan
                  </span>
                )}
            </p>

            {/* Price section - Only show if not organization plan */}
            {!plan.isOrganizationPlan && (
              <div className="mb-4">
                {plan.id === 'enterprise' ? (
                  <div className="text-2xl font-bold group-hover:text-white">
                    Custom Pricing
                  </div>
                ) : (
                  <>
                    <div className="text-3xl font-bold group-hover:text-white">
                      ₹{currentPrice.toLocaleString()}
                      <span className="text-lg font-normal text-gray-600 group-hover:text-gray-300">
                        /{billingCycle === 'monthly' ? 'month' : 'year'}
                      </span>
                    </div>
                    {plan.savings && billingCycle === 'yearly' && (
                      <div className="text-sm text-green-600 group-hover:text-green-400 font-medium mt-1">
                        {plan.savings}
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
          </div>

          <div className="space-y-3 mb-6">
            {plan.isOrganizationPlan ? (
              // Show HTML description for organization plans
              <div className="prose prose-sm max-w-none group-hover:text-white">
                {plan.apiPlan?.description ? (
                  <div className="text-gray-700 group-hover:text-white text-sm">
                    {renderHTMLContent(plan.apiPlan.description, true)}
                  </div>
                ) : (
                  <p className="text-sm text-gray-700 group-hover:text-white">
                    Custom organization plan with tailored features and pricing.
                  </p>
                )}
              </div>
            ) : // Show hierarchical features for regular plans
            plan.apiPlan?.features ? (
              <div className="space-y-4">
                {/* MRD Module */}
                {plan.apiPlan.features.MRD &&
                  plan.apiPlan.features.MRD.length > 0 && (
                    <div>
                      <div className="flex items-center space-x-2 mb-2">
                        <GreenCheckIcon
                          width={17}
                          height={17}
                          className="group-hover:text-white"
                        />
                        <span className="text-sm font-semibold text-gray-700 group-hover:text-white">
                          MRD
                        </span>
                      </div>
                      <div className="ml-6 space-y-1">
                        {plan.apiPlan.features.MRD.map(
                          (feature: any, index: number) => (
                            <div
                              key={index}
                              className="flex items-center space-x-2"
                            >
                              <GreenCheckIcon
                                width={12}
                                height={12}
                                className="group-hover:text-white"
                              />
                              <span className="text-xs text-gray-600 group-hover:text-gray-300">
                                {feature.featureName}
                              </span>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}

                {/* EMR Module */}
                {plan.apiPlan.features.EMR &&
                  plan.apiPlan.features.EMR.length > 0 && (
                    <div>
                      <div className="flex items-center space-x-2 mb-2">
                        <GreenCheckIcon
                          width={17}
                          height={17}
                          className="group-hover:text-white"
                        />
                        <span className="text-sm font-semibold text-gray-700 group-hover:text-white">
                          EMR
                        </span>
                      </div>
                      <div className="ml-6 space-y-1">
                        {plan.apiPlan.features.EMR.map(
                          (feature: any, index: number) => (
                            <div
                              key={index}
                              className="flex items-center space-x-2"
                            >
                              <GreenCheckIcon
                                width={12}
                                height={12}
                                className="group-hover:text-white"
                              />
                              <span className="text-xs text-gray-600 group-hover:text-gray-300">
                                {feature.featureName}
                              </span>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}

                {/* Billing Module */}
                {plan.apiPlan.features.Billing &&
                  plan.apiPlan.features.Billing.length > 0 && (
                    <div>
                      <div className="flex items-center space-x-2 mb-2">
                        <GreenCheckIcon
                          width={17}
                          height={17}
                          className="group-hover:text-white"
                        />
                        <span className="text-sm font-semibold text-gray-700 group-hover:text-white">
                          Billing
                        </span>
                      </div>
                      <div className="ml-6 space-y-1">
                        {plan.apiPlan.features.Billing.map(
                          (feature: any, index: number) => (
                            <div
                              key={index}
                              className="flex items-center space-x-2"
                            >
                              <GreenCheckIcon
                                width={12}
                                height={12}
                                className="group-hover:text-white"
                              />
                              <span className="text-xs text-gray-600 group-hover:text-gray-300">
                                {feature.featureName}
                              </span>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}

                {/* Add On Features Available */}
                {hasAddOnFeatures(plan) && (
                  <div className="flex items-center space-x-3 mt-3">
                    <GreenCheckIcon
                      width={16}
                      height={16}
                      className="group-hover:text-white"
                    />
                    <span className="text-sm text-gray-700 group-hover:text-white font-medium">
                      Add on features available
                    </span>
                  </div>
                )}
              </div>
            ) : (
              // Fallback to old string array format if apiPlan.features is not available
              <>
                {plan.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <GreenCheckIcon
                      width={16}
                      height={16}
                      className="group-hover:text-white"
                    />
                    <span className="text-sm text-gray-700 group-hover:text-gray-300">
                      {feature}
                    </span>
                  </div>
                ))}
                {/* Add On Features Available for fallback format */}
                {hasAddOnFeatures(plan) && (
                  <div className="flex items-center space-x-3 mt-3">
                    <GreenCheckIcon
                      width={16}
                      height={16}
                      className="group-hover:text-white"
                    />
                    <span className="text-sm text-gray-700 group-hover:text-white font-medium">
                      Add on features available
                    </span>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        <div className="mt-auto">
          <button
            onClick={handleButtonClick}
            disabled={isLoading}
            className="w-full py-3 px-4 rounded-lg font-medium text-white transition-all duration-200 hover:opacity-90 disabled:opacity-50"
            style={{ backgroundColor: plan.buttonColor }}
          >
            {isLoading ? 'Processing...' : buttonText}
          </button>
        </div>
      </div>

      {/* Subscription Details Modal */}
      <SubscriptionDetailsModal
        open={showModal}
        onClose={handleModalClose}
        plan={plan}
        billingCycle={billingCycle}
        nextPlan={nextPlan}
        currentPlanId={currentPlanId}
        allPlans={allPlans}
        subscriberData={subscriberData}
        pricing={pricing}
        selectedPlanId={selectedPlan || undefined}
        billingDetails={billingDetails}
        onBillingDetailsChange={(field, value) =>
          setBillingDetails((prev) => ({ ...prev, [field]: value }))
        }
        onBillingCycleChange={onBillingCycleChange}
        onProceed={handleProceed}
        onUpgradeClick={handleUpgradeClick}
        onAddFeaturesClick={handleAddFeaturesClick}
        isLoading={isLoading || isTrialLoading}
        isFromProfile={isFromProfile}
      />

      {/* Upgrade Modal */}
      <UpgradeModal
        open={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        plan={plan}
        nextPlan={nextPlan}
        billingCycle={billingCycle}
        billingDetails={billingDetails}
        onBillingDetailsChange={(field: string, value: string) =>
          setBillingDetails((prev) => ({ ...prev, [field]: value }))
        }
        onProceed={() => {
          if (nextPlan) {
            onPlanSelect(nextPlan.id);
            setShowUpgradeModal(false);
            setShowModal(true);
          }
        }}
        onAddFeaturesClick={handleAddFeaturesClick}
      />

      {/* Add Features Modal */}
      <AddFeaturesModal
        open={showAddFeaturesModal}
        onClose={() => setShowAddFeaturesModal(false)}
        plan={addFeaturesPlan}
        currentPlanId={currentPlanId}
        subscriberData={subscriberData}
      />

      {/* Payment Success Modal */}
      <PaymentSuccessModal
        open={showPaymentSuccessModal}
        onClose={() => setShowPaymentSuccessModal(false)}
        isTrial={currentPrice === 0 && !plan.isOrganizationPlan}
        billingCycle={billingCycle}
        isFromProfile={isFromProfile}
      />

      {/* Payment Failure Modal - Updated for trial errors */}
      <PaymentFailureModal
        open={showPaymentFailureModal}
        onClose={() => {
          setShowPaymentFailureModal(false);
          setTrialErrorMessage('');
        }}
        errorMessage={trialErrorMessage}
        isFromProfile={isFromProfile}
      />

      {/* Free Trial Success Modal */}
      <FreeTrialSuccessModal
        open={showFreeTrialSuccessModal}
        onClose={() => setShowFreeTrialSuccessModal(false)}
      />
    </>
  );
};

export default PlanCard;
